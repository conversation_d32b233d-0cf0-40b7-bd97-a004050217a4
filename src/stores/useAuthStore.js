import { create } from "zustand";
import supabase from "@/lib/supabaseClient";
import { userService } from "@/services/api/userService";
import * as Sentry from "@sentry/nextjs";

export const useAuthStore = create((set, get) => ({
  user: null,
  error: null,

  // AppSumo 激活错误状态
  appsumoActivationError: null,

  // 清除 AppSumo 激活错误
  clearAppSumoActivationError: () => set({ appsumoActivationError: null }),

  // 检查并激活AppSumo license
  checkAndActivateAppSumo: async () => {
    const appsumoCode = localStorage.getItem("appsumo_auth_code");
    if (appsumoCode) {
      try {
        const { appsumoService } = await import("@/services");
        await appsumoService.activateLicense(appsumoCode);
        localStorage.removeItem("appsumo_auth_code");
        // 设置需要显示 onboarding 的标记
        localStorage.setItem("appsumo_onboarding_needed", "true");

        // 激活成功后，重新获取用户信息以确保 isAppSumoUser 为 true
        try {
          const { data: userData } = await userService.getUser();
          set({ user: userData });
          console.log(
            "User data refreshed after AppSumo activation:",
            userData
          );
        } catch (error) {
          console.error(
            "Failed to refresh user data after AppSumo activation:",
            error
          );
        }
      } catch (error) {
        console.error("Failed to activate AppSumo license:", error);

        // 设置激活错误状态，用于在 Dashboard 中显示错误弹窗
        if (error?.data?.code === 50007) {
          // License已被其他账号激活
          set({ appsumoActivationError: "50007" });
        } else if (error?.data?.code === 50008) {
          // OAuth错误
          // set({ appsumoActivationError: "50008" });
          console.log("OAuth error during AppSumo activation:", error);
        } else {
          // 其他错误
          set({ appsumoActivationError: "unknown" });
        }

        // 清除授权码
        localStorage.removeItem("appsumo_auth_code");
      }
    }
  },

  initialize: async () => {
    try {
      // 获取会话但不使用返回值，因为我们在onAuthStateChange中处理会话
      await supabase.auth.getSession();

      supabase.auth.onAuthStateChange(async (event, session) => {
        if (!session) {
          set({ user: null });
          Sentry.setUser(null);
          return;
        }

        try {
          if (event === "INITIAL_SESSION" || event === "SIGNED_IN") {
            const { data: userData } = await userService.getUser();
            set({ user: userData });
            if (userData && userData.id) {
              Sentry.setUser({
                id: userData.id,
                email: userData?.email,
                isAnonymous: userData?.isAnonymous,
              });
            } else {
              Sentry.setUser(null);
            }
            // 检查并激活AppSumo license（仅对非匿名用户）
            if (!userData?.isAnonymous) {
              const { checkAndActivateAppSumo } = get();
              await checkAndActivateAppSumo();
            }
          }
        } catch (error) {
          console.error("Failed to get user:", error);
          set({ user: null, error: error.message });
          Sentry.setUser(null);
        }
      });
    } catch (error) {
      console.error("Initialize error:", error);
      set({ error: error.message });
    }
  },

  signInWithGoogle: async (locale) => {
    try {
      set({ error: null });

      // 在开发环境下使用环境变量中的站点URL，生产环境使用当前origin
      const siteUrl =
        process.env.NODE_ENV === "development"
          ? process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
          : window.location.origin;

      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${siteUrl}/${locale}/auth/callback`,
        },
      });

      if (error) throw error;
    } catch (error) {
      set({ error: error.message });
      throw error;
    }
  },

  signOut: async () => {
    try {
      await supabase.auth.signOut();
      set({ user: null, error: null });
    } catch (error) {
      console.error("Sign out error:", error);
      set({ error: error.message });
    }
  },

  clearError: () => set({ error: null }),

  signUpWithEmail: async (email, password, firstName, lastName, locale) => {
    try {
      // 在开发环境下使用环境变量中的站点URL，生产环境使用当前origin
      const siteUrl =
        process.env.NODE_ENV === "development"
          ? process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
          : window.location.origin;

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
          },
          emailRedirectTo: `${siteUrl}/${locale}/auth/callback?type=email_verification`,
        },
      });

      if (error) {
        return { error };
      }

      return { data };
    } catch (error) {
      return { error };
    }
  },

  signInWithEmail: async (email, password) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { error };
      }

      set({ session: data.session });
      return { data };
    } catch (error) {
      return { error };
    }
  },

  resetPassword: async (email, locale) => {
    try {
      // 在开发环境下使用环境变量中的站点URL，生产环境使用当前origin
      const siteUrl =
        process.env.NODE_ENV === "development"
          ? process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
          : window.location.origin;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${siteUrl}/${locale}/auth/reset-password`,
      });

      if (error) throw error;
    } catch (error) {
      throw new Error(error.message);
    }
  },

  resendVerificationEmail: async (email, locale) => {
    if (!email) {
      throw new Error("Email is required");
    }

    try {
      // 在开发环境下使用环境变量中的站点URL，生产环境使用当前origin
      const siteUrl =
        process.env.NODE_ENV === "development"
          ? process.env.NEXT_PUBLIC_SITE_URL || window.location.origin
          : window.location.origin;

      const { error } = await supabase.auth.resend({
        type: "signup",
        email: email,
        options: {
          emailRedirectTo: `${siteUrl}/${locale}/auth/callback`,
        },
      });

      if (error) throw error;
    } catch (error) {
      console.error("Failed to resend verification email:", error);
      throw error;
    }
  },

  updatePassword: async (password) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) throw error;
    } catch (error) {
      throw new Error(error.message);
    }
  },
}));

useAuthStore.getState().initialize();
