"use client";

import { useEffect, useState } from "react";

export default function DebugEnvPage() {
  const [envInfo, setEnvInfo] = useState({});

  useEffect(() => {
    setEnvInfo({
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
      windowOrigin: window.location.origin,
      windowHref: window.location.href,
    });
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Debug</h1>
      <div className="space-y-2">
        <p><strong>NODE_ENV:</strong> {envInfo.NODE_ENV}</p>
        <p><strong>NEXT_PUBLIC_SITE_URL:</strong> {envInfo.NEXT_PUBLIC_SITE_URL}</p>
        <p><strong>window.location.origin:</strong> {envInfo.windowOrigin}</p>
        <p><strong>window.location.href:</strong> {envInfo.windowHref}</p>
      </div>
    </div>
  );
}
